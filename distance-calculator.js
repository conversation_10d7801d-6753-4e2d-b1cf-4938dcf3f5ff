/**
 * 计算两个经纬度点之间的直线距离（使用Haversine公式）
 * @param {number} lat1 - 第一个点的纬度
 * @param {number} lon1 - 第一个点的经度
 * @param {number} lat2 - 第二个点的纬度
 * @param {number} lon2 - 第二个点的经度
 * @param {string} unit - 距离单位 ('km' 公里, 'm' 米, 'mi' 英里)
 * @returns {number} 两点间的距离
 */
function calculateDistance(lat1, lon1, lat2, lon2, unit = 'km') {
    // 地球半径（公里）
    const R = 6371;
    
    // 将角度转换为弧度
    const dLat = toRadians(lat2 - lat1);
    const dLon = toRadians(lon2 - lon1);
    const lat1Rad = toRadians(lat1);
    const lat2Rad = toRadians(lat2);
    
    // Haversine公式
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    
    // 计算距离（公里）
    let distance = R * c;
    
    // 根据单位转换
    switch (unit.toLowerCase()) {
        case 'm':
            distance = distance * 1000; // 转换为米
            break;
        case 'mi':
            distance = distance * 0.621371; // 转换为英里
            break;
        case 'km':
        default:
            // 默认为公里，不需要转换
            break;
    }
    
    return distance;
}

/**
 * 将角度转换为弧度
 * @param {number} degrees - 角度值
 * @returns {number} 弧度值
 */
function toRadians(degrees) {
    return degrees * (Math.PI / 180);
}

/**
 * 格式化距离输出
 * @param {number} distance - 距离值
 * @param {string} unit - 单位
 * @returns {string} 格式化后的距离字符串
 */
function formatDistance(distance, unit = 'km') {
    const unitNames = {
        'km': '公里',
        'm': '米',
        'mi': '英里'
    };
    
    return `${distance.toFixed(2)} ${unitNames[unit] || unit}`;
}

// 使用示例
if (typeof module !== 'undefined' && module.exports) {
    // Node.js 环境
    module.exports = {
        calculateDistance,
        formatDistance,
        toRadians
    };
} else {
    // 浏览器环境 - 函数已经在全局作用域中
    console.log('距离计算器已加载');
}

// 示例用法
console.log('=== 距离计算示例 ===');

// 北京到上海的距离
const beijing = { lat: 39.9042, lon: 116.4074 };
const shanghai = { lat: 31.2304, lon: 121.4737 };

const distanceKm = calculateDistance(beijing.lat, beijing.lon, shanghai.lat, shanghai.lon, 'km');
const distanceM = calculateDistance(beijing.lat, beijing.lon, shanghai.lat, shanghai.lon, 'm');
const distanceMi = calculateDistance(beijing.lat, beijing.lon, shanghai.lat, shanghai.lon, 'mi');

console.log(`北京到上海的距离:`);
console.log(`- ${formatDistance(distanceKm, 'km')}`);
console.log(`- ${formatDistance(distanceM, 'm')}`);
console.log(`- ${formatDistance(distanceMi, 'mi')}`);

// 广州到深圳的距离
const guangzhou = { lat: 23.1291, lon: 113.2644 };
const shenzhen = { lat: 22.5431, lon: 114.0579 };

const gzToSz = calculateDistance(guangzhou.lat, guangzhou.lon, shenzhen.lat, shenzhen.lon);
console.log(`\n广州到深圳的距离: ${formatDistance(gzToSz, 'km')}`);

export { calculateDistance, formatDistance };
