# Docker 快速上手指南

## 🐳 什么是 Docker

Docker 是一个开源的容器化平台，可以将应用程序及其依赖项打包到轻量级、可移植的容器中。

## 📦 核心概念

### 镜像 (Image)
- 只读的模板，用于创建容器
- 包含运行应用所需的所有内容：代码、运行时、库、环境变量等

### 容器 (Container)
- 镜像的运行实例
- 轻量级、可移植的执行环境

### Dockerfile
- 用于构建镜像的文本文件
- 包含一系列指令来组装镜像

## 🚀 安装 Docker

### Windows/Mac
1. 下载 Docker Desktop
2. 运行安装程序
3. 启动 Docker Desktop

### Linux (Ubuntu)
```bash
# 更新包索引
sudo apt update

# 安装必要的包
sudo apt install apt-transport-https ca-certificates curl software-properties-common

# 添加 Docker 官方 GPG 密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -

# 添加 Docker 仓库
sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"

# 安装 Docker CE
sudo apt update
sudo apt install docker-ce
```

## 🔧 基本命令

### 镜像操作
```bash
# 拉取镜像
docker pull nginx

# 查看本地镜像
docker images

# 删除镜像
docker rmi nginx

# 构建镜像
docker build -t myapp .
```

### 容器操作
```bash
# 运行容器
docker run -d -p 80:80 nginx

# 查看运行中的容器
docker ps

# 查看所有容器
docker ps -a

# 停止容器
docker stop container_id

# 删除容器
docker rm container_id

# 进入容器
docker exec -it container_id /bin/bash
```

## 📝 Dockerfile 示例

### Node.js 应用
```dockerfile
# 使用官方 Node.js 镜像
FROM node:16

# 设置工作目录
WORKDIR /app

# 复制 package.json
COPY package*.json ./

# 安装依赖
RUN npm install

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["npm", "start"]
```

### Python 应用
```dockerfile
FROM python:3.9

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "app.py"]
```

## 🔗 Docker Compose

用于定义和运行多容器 Docker 应用的工具。

### docker-compose.yml 示例
```yaml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "3000:3000"
    depends_on:
      - db
    environment:
      - DATABASE_URL=**********************************/mydb

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=mydb
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

### Compose 命令
```bash
# 启动服务
docker-compose up

# 后台启动
docker-compose up -d

# 停止服务
docker-compose down

# 查看日志
docker-compose logs
```

## 💡 最佳实践

1. **使用多阶段构建**减小镜像大小
2. **使用 .dockerignore**排除不必要的文件
3. **不要在容器中存储数据**，使用数据卷
4. **使用具体的标签**而不是 latest
5. **最小化层数**，合并 RUN 指令
6. **使用非 root 用户**运行应用

## 🔍 常用调试命令

```bash
# 查看容器日志
docker logs container_id

# 查看容器详细信息
docker inspect container_id

# 查看容器资源使用情况
docker stats

# 清理未使用的资源
docker system prune
```

## 📚 学习资源

- [Docker 官方文档](https://docs.docker.com/)
- [Docker Hub](https://hub.docker.com/)
- [Play with Docker](https://labs.play-with-docker.com/)

---

*Docker 让应用部署变得简单而一致！* 🚀
