# Pixel 8 Pro ADB 刷机完整教程

## 前言警告 ⚠️

**刷机有风险，操作需谨慎！**
- 刷机会清除所有数据，请提前备份重要文件
- 解锁 Bootloader 会失去保修
- 操作不当可能导致设备变砖
- 请确保您了解风险并有一定的技术基础

## 准备工作

### 1. 硬件准备
- Pixel 8 Pro 手机
- 原装 USB-C 数据线（建议使用原装线，避免连接问题）
- Windows/Mac/Linux 电脑

### 2. 软件准备

#### 下载 ADB 和 Fastboot 工具
- **官方下载**：[Android SDK Platform Tools](https://developer.android.com/studio/releases/platform-tools)
- 解压到易于访问的目录（如 `C:\adb` 或 `~/adb`）

#### 下载官方固件
- 访问 [Google 官方固件页面](https://developers.google.com/android/images)
- 找到 Pixel 8 Pro (husky) 对应的固件版本
- 下载最新的 Factory Image

#### 安装驱动（Windows 用户）
- 下载 [Google USB Driver](https://developer.android.com/studio/run/win-usb)
- 或使用通用 ADB 驱动

### 3. 手机设置准备

#### 开启开发者选项
1. 进入 `设置` → `关于手机`
2. 连续点击 `版本号` 7次
3. 输入锁屏密码激活开发者选项

#### 开启必要选项
1. 进入 `设置` → `系统` → `开发者选项`
2. 开启 `USB 调试`
3. 开启 `OEM 解锁`（重要！）

## 刷机步骤

### 第一步：验证 ADB 连接

1. 手机连接电脑，选择 `文件传输` 模式
2. 打开命令行/终端，进入 ADB 工具目录
3. 执行命令验证连接：

```bash
adb devices
```

应该显示类似输出：
```
List of devices attached
1A2B3C4D5E6F7G8H    device
```

### 第二步：进入 Fastboot 模式

执行命令重启到 Bootloader：
```bash
adb reboot bootloader
```

或者手动进入：
- 关机状态下同时按住 `音量下键` + `电源键`

### 第三步：解锁 Bootloader

⚠️ **重要警告**：此步骤会清除所有数据！

1. 在 Fastboot 模式下执行：
```bash
fastboot flashing unlock
```

2. 手机屏幕会显示警告信息
3. 使用音量键选择 `Unlock the bootloader`
4. 按电源键确认
5. 设备会自动重启并恢复出厂设置

### 第四步：准备刷机文件

1. 解压下载的官方固件包
2. 将解压后的文件放在 ADB 工具同一目录
3. 确认包含以下文件：
   - `flash-all.bat` (Windows) 或 `flash-all.sh` (Mac/Linux)
   - `bootloader-husky-*.img`
   - `radio-husky-*.img`
   - `image-husky-*.zip`

### 第五步：执行刷机

#### 方法一：使用官方脚本（推荐）

**Windows 用户：**
```bash
flash-all.bat
```

**Mac/Linux 用户：**
```bash
chmod +x flash-all.sh
./flash-all.sh
```

#### 方法二：手动刷入各分区

如果自动脚本失败，可以手动执行：

```bash
# 刷入 Bootloader
fastboot flash bootloader bootloader-husky-*.img
fastboot reboot-bootloader

# 刷入基带
fastboot flash radio radio-husky-*.img
fastboot reboot-bootloader

# 解压并刷入系统镜像
fastboot -w update image-husky-*.zip
```

### 第六步：完成刷机

1. 刷机完成后设备会自动重启
2. 首次开机可能需要较长时间（5-10分钟）
3. 按照设置向导完成初始配置

## 重要注意事项

### 刷机过程中的注意点
- **保持连接稳定**：确保 USB 线连接牢固
- **不要断电**：刷机过程中不要断开电源或数据线
- **耐心等待**：刷机过程可能需要 10-20 分钟
- **观察输出**：注意命令行输出，如有错误及时处理

### 常见问题解决

#### 1. 设备未识别
- 检查驱动是否正确安装
- 尝试更换 USB 端口或数据线
- 确认手机已开启 USB 调试

#### 2. Fastboot 命令失败
- 确认设备在 Fastboot 模式
- 检查固件文件是否完整
- 尝试使用管理员权限运行命令

#### 3. 刷机中断或失败
- 重新进入 Fastboot 模式
- 重新执行刷机命令
- 如果多次失败，尝试下载其他版本固件

### 安全建议

1. **备份重要数据**：刷机前务必备份照片、联系人等
2. **记录原始信息**：记录原始系统版本、基带版本等
3. **保留原始固件**：保存原始固件文件以备恢复
4. **分步操作**：不要急于求成，按步骤仔细操作

## 刷机后的设置

### 重新锁定 Bootloader（可选）

如果希望恢复保修状态：
```bash
fastboot flashing lock
```

⚠️ 注意：锁定前确保系统运行正常

### 系统优化建议

1. 重新设置开发者选项（如需要）
2. 恢复应用和数据
3. 检查系统更新
4. 安装必要的安全补丁

## 总结

Pixel 8 Pro 的 ADB 刷机过程相对简单，但需要谨慎操作。关键步骤包括：
1. 准备工具和固件
2. 解锁 Bootloader
3. 使用 Fastboot 刷入固件
4. 完成系统设置

记住：**安全第一，备份重要，谨慎操作！**

## 附录：禁用系统更新

### 为什么要禁用系统更新？

- 保持当前稳定的系统版本
- 避免新版本可能带来的问题
- 保持 Root 权限或自定义修改
- 控制更新时机

### 方法一：开发者选项（简单）

1. 开启开发者选项（连续点击版本号7次）
2. 进入 `设置` → `系统` → `开发者选项`
3. 找到 `自动系统更新` 并关闭

⚠️ **注意**：此方法可能在系统重启后失效

### 方法二：ADB 命令禁用（推荐）

#### 准备工作
- 确保已安装 ADB 工具
- 开启 USB 调试
- 连接手机到电脑

#### 禁用系统更新服务

```bash
# 禁用 Google 系统更新服务
adb shell pm disable-user com.google.android.gms.update

# 禁用 OTA 更新服务
adb shell pm disable-user com.google.android.gsf.update

# 禁用系统更新应用
adb shell pm disable-user com.android.systemupdater

# 禁用 Google Play 系统更新
adb shell pm disable-user com.google.android.gms.update.SystemUpdateService
```

#### 验证禁用状态

```bash
# 检查服务状态
adb shell pm list packages -d | grep update
```

### 方法三：冻结更新应用

```bash
# 冻结系统更新器
adb shell pm disable-user --user 0 com.android.systemupdater

# 冻结 Google Play 服务更新组件
adb shell pm disable-user --user 0 com.google.android.gms.update
```

### 恢复系统更新

如果需要重新启用更新：

```bash
# 启用系统更新服务
adb shell pm enable com.google.android.gms.update
adb shell pm enable com.google.android.gsf.update
adb shell pm enable com.android.systemupdater
```

### 注意事项

1. **安全风险**：禁用更新可能错过重要安全补丁
2. **功能影响**：某些应用可能需要最新系统版本
3. **可逆操作**：所有禁用操作都可以恢复
4. **定期检查**：建议定期手动检查重要更新

---

*最后更新：2025年1月*
*适用设备：Google Pixel 8 Pro (husky)*
